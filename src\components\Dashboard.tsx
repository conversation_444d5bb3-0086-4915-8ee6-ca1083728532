import React, { useState, useEffect } from "react";
import { FruitEntry, EntryStatus, FilterOptions } from "../types";
import { loadEntries } from "../utils/storage";
import { getEntryStatus, statusColors, statusLabels } from "../utils/status";
import { exportToExcel } from "../utils/excel";

const Dashboard: React.FC = () => {
  const [entries, setEntries] = useState<FruitEntry[]>([]);
  const [filters, setFilters] = useState<FilterOptions>({
    searchTerm: "",
    statusFilter: "all",
  });

  useEffect(() => {
    const loadData = () => {
      setEntries(loadEntries());
    };

    loadData();

    // Refresh data every 2 seconds to show real-time updates
    const interval = setInterval(loadData, 2000);
    return () => clearInterval(interval);
  }, []);

  const handleExport = () => {
    if (entries.length === 0) {
      alert("No data to export");
      return;
    }
    exportToExcel(entries);
  };

  const handleSearchChange = (value: string) => {
    setFilters((prev) => ({ ...prev, searchTerm: value }));
  };

  const handleStatusFilterChange = (value: EntryStatus | "all") => {
    setFilters((prev) => ({ ...prev, statusFilter: value }));
  };

  // Filter entries based on search and status
  const filteredEntries = entries.filter((entry) => {
    const matchesSearch = entry.farmerCode
      .toLowerCase()
      .includes(filters.searchTerm.toLowerCase());
    const matchesStatus =
      filters.statusFilter === "all" ||
      getEntryStatus(entry) === filters.statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Sort entries by timestamp (oldest first to show serial order)
  const sortedEntries = [...filteredEntries].sort(
    (a, b) => a.timestamp - b.timestamp
  );

  // Get status counts for summary
  const statusCounts = entries.reduce((acc, entry) => {
    const status = getEntryStatus(entry);
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<EntryStatus, number>);

  return (
    <div className="space-y-6">
      {/* Header with Export Button */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900">
          Dashboard
        </h2>
        <button
          onClick={handleExport}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm sm:text-base"
        >
          📊 Export to Excel
        </button>
      </div>

      {/* Status Summary Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 sm:p-4">
          <div className="text-red-800 text-xs sm:text-sm font-medium">
            Incomplete
          </div>
          <div className="text-red-900 text-xl sm:text-2xl font-bold">
            {statusCounts.incomplete || 0}
          </div>
        </div>
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 sm:p-4">
          <div className="text-orange-800 text-xs sm:text-sm font-medium">
            Grading Complete
          </div>
          <div className="text-orange-900 text-xl sm:text-2xl font-bold">
            {statusCounts["grading-complete"] || 0}
          </div>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4">
          <div className="text-yellow-800 text-xs sm:text-sm font-medium">
            Weight Added
          </div>
          <div className="text-yellow-900 text-xl sm:text-2xl font-bold">
            {statusCounts["weight-added"] || 0}
          </div>
        </div>
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 sm:p-4">
          <div className="text-green-800 text-xs sm:text-sm font-medium">
            Complete
          </div>
          <div className="text-green-900 text-xl sm:text-2xl font-bold">
            {statusCounts.complete || 0}
          </div>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <div className="bg-white shadow rounded-lg p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row gap-4 mb-4 sm:mb-6">
          <div className="flex-1">
            <label
              htmlFor="search"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Search by Farmer Code
            </label>
            <input
              id="search"
              type="text"
              value={filters.searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              placeholder="Enter farmer code to search..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="sm:w-48">
            <label
              htmlFor="status-filter"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Filter by Status
            </label>
            <select
              id="status-filter"
              value={filters.statusFilter}
              onChange={(e) =>
                handleStatusFilterChange(e.target.value as EntryStatus | "all")
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="incomplete">Incomplete</option>
              <option value="grading-complete">Grading Complete</option>
              <option value="weight-added">Weight Added</option>
              <option value="complete">Complete</option>
            </select>
          </div>
        </div>

        {/* Main Data Table - Desktop */}
        <div className="hidden md:block overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Serial No.
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Farmer Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unripe
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Under Ripe
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Waste
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Long Stalk
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Weight
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Cluster
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedEntries.map((entry, index) => {
                const status = getEntryStatus(entry);
                return (
                  <tr key={entry.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {index + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {entry.farmerCode}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {entry.unripe ?? "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {entry.underRipe ?? "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {entry.waste ?? "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {entry.longStalk ?? "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {entry.weight ? `${entry.weight} kg` : "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {entry.cluster || "-"}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${statusColors[status]}`}
                      >
                        {statusLabels[status]}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          {sortedEntries.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {entries.length === 0
                ? "No entries available."
                : "No entries match your search criteria."}
            </div>
          )}
        </div>

        {/* Mobile Card Layout */}
        <div className="md:hidden space-y-4">
          {sortedEntries.map((entry, index) => {
            const status = getEntryStatus(entry);
            return (
              <div key={entry.id} className="bg-gray-50 rounded-lg p-4 border">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <div className="text-sm text-gray-500">#{index + 1}</div>
                    <div className="font-semibold text-lg">
                      {entry.farmerCode}
                    </div>
                  </div>
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${statusColors[status]}`}
                  >
                    {statusLabels[status]}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-3 text-sm">
                  <div>
                    <span className="text-gray-500">Unripe:</span>
                    <span className="ml-1 font-medium">
                      {entry.unripe ?? "-"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Under Ripe:</span>
                    <span className="ml-1 font-medium">
                      {entry.underRipe ?? "-"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Waste:</span>
                    <span className="ml-1 font-medium">
                      {entry.waste ?? "-"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Long Stalk:</span>
                    <span className="ml-1 font-medium">
                      {entry.longStalk ?? "-"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Weight:</span>
                    <span className="ml-1 font-medium">
                      {entry.weight ? `${entry.weight} kg` : "-"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Cluster:</span>
                    <span className="ml-1 font-medium">
                      {entry.cluster || "-"}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
          {sortedEntries.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              {entries.length === 0
                ? "No entries available."
                : "No entries match your search criteria."}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
