import React, { useState, useEffect } from 'react';
import { FruitEntry } from '../types';
import { loadEntries, addEntry, updateEntry, generateId } from '../utils/storage';
import { getEntryStatus, statusColors, statusLabels } from '../utils/status';

const OperatorScreen: React.FC = () => {
  const [entries, setEntries] = useState<FruitEntry[]>([]);
  const [newFarmerCode, setNewFarmerCode] = useState('');
  const [weightInputs, setWeightInputs] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    setEntries(loadEntries());
  }, []);

  const handleAddFarmerCode = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newFarmerCode.trim()) return;

    const newEntry: FruitEntry = {
      id: generateId(),
      farmerCode: newFarmerCode.trim(),
      unripe: null,
      underRipe: null,
      waste: null,
      longStalk: null,
      weight: null,
      cluster: null,
      timestamp: Date.now(),
    };

    addEntry(newEntry);
    setEntries(loadEntries());
    setNewFarmerCode('');
  };

  const handleWeightUpdate = (entryId: string) => {
    const weightValue = weightInputs[entryId];
    if (!weightValue || isNaN(Number(weightValue))) return;

    updateEntry(entryId, { weight: Number(weightValue) });
    setEntries(loadEntries());
    setWeightInputs({ ...weightInputs, [entryId]: '' });
  };

  const handleWeightInputChange = (entryId: string, value: string) => {
    setWeightInputs({ ...weightInputs, [entryId]: value });
  };

  // Sort entries by timestamp (newest first for display)
  const sortedEntries = [...entries].sort((a, b) => b.timestamp - a.timestamp);

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Operator Panel</h2>
        
        {/* Add Farmer Code Form */}
        <div className="mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Farmer Code</h3>
          <form onSubmit={handleAddFarmerCode} className="flex gap-4">
            <input
              type="text"
              value={newFarmerCode}
              onChange={(e) => setNewFarmerCode(e.target.value)}
              placeholder="Enter farmer code"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              type="submit"
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            >
              Add Entry
            </button>
          </form>
        </div>

        {/* Entries Table */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">All Entries</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Farmer Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Weight
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Update Weight
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entry Time
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedEntries.map((entry) => {
                  const status = getEntryStatus(entry);
                  return (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {entry.farmerCode}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {entry.weight ? `${entry.weight} kg` : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex gap-2">
                          <input
                            type="number"
                            step="0.1"
                            value={weightInputs[entry.id] || ''}
                            onChange={(e) => handleWeightInputChange(entry.id, e.target.value)}
                            placeholder="Weight (kg)"
                            className="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                          <button
                            onClick={() => handleWeightUpdate(entry.id)}
                            className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-1 focus:ring-green-500"
                          >
                            Update
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${statusColors[status]}`}>
                          {statusLabels[status]}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(entry.timestamp).toLocaleString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
            {sortedEntries.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No entries yet. Add a farmer code to get started.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OperatorScreen;
