import * as XLSX from 'xlsx';
import { FruitEntry } from '../types';
import { getEntryStatus, statusLabels } from './status';

export const exportToExcel = (entries: FruitEntry[]): void => {
  // Sort entries by timestamp (oldest first)
  const sortedEntries = [...entries].sort((a, b) => a.timestamp - b.timestamp);
  
  // Transform data for Excel
  const excelData = sortedEntries.map((entry, index) => ({
    'Serial No.': index + 1,
    'Farmer Code': entry.farmerCode,
    'Unripe': entry.unripe ?? '',
    'Under Ripe': entry.underRipe ?? '',
    'Waste': entry.waste ?? '',
    'Long Stalk': entry.longStalk ?? '',
    'Weight': entry.weight ?? '',
    'Cluster': entry.cluster ?? '',
    'Status': statusLabels[getEntryStatus(entry)],
    'Entry Time': new Date(entry.timestamp).toLocaleString()
  }));

  // Create workbook and worksheet
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.json_to_sheet(excelData);

  // Set column widths
  const colWidths = [
    { wch: 10 }, // Serial No.
    { wch: 15 }, // Farmer Code
    { wch: 10 }, // Unripe
    { wch: 12 }, // Under Ripe
    { wch: 10 }, // Waste
    { wch: 12 }, // Long Stalk
    { wch: 10 }, // Weight
    { wch: 10 }, // Cluster
    { wch: 15 }, // Status
    { wch: 20 }  // Entry Time
  ];
  ws['!cols'] = colWidths;

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(wb, ws, 'Fruit Collection Data');

  // Generate filename with current date
  const now = new Date();
  const filename = `fruit-collection-${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}.xlsx`;

  // Save file
  XLSX.writeFile(wb, filename);
};
