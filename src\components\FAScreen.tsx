import React, { useState, useEffect } from 'react';
import { FruitEntry } from '../types';
import { loadEntries, updateEntry } from '../utils/storage';
import { getEntryStatus, statusColors, statusLabels } from '../utils/status';

const FAScreen: React.FC = () => {
  const [entries, setEntries] = useState<FruitEntry[]>([]);
  const [clusterInputs, setClusterInputs] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    setEntries(loadEntries());
  }, []);

  const handleClusterInputChange = (entryId: string, value: string) => {
    setClusterInputs({ ...clusterInputs, [entryId]: value });
  };

  const handleClusterUpdate = (entryId: string) => {
    const clusterValue = clusterInputs[entryId];
    if (!clusterValue || !clusterValue.trim()) return;

    updateEntry(entryId, { cluster: clusterValue.trim() });
    setEntries(loadEntries());
    setClusterInputs({ ...clusterInputs, [entryId]: '' });
  };

  // Sort entries by timestamp (newest first for display)
  const sortedEntries = [...entries].sort((a, b) => b.timestamp - a.timestamp);

  return (
    <div className="space-y-6">
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">FA Panel</h2>
        
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Enter Cluster Numbers</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Farmer Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Grading Details
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Weight
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Cluster
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Update Cluster
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entry Time
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedEntries.map((entry) => {
                  const status = getEntryStatus(entry);
                  return (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {entry.farmerCode}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="text-xs space-y-1">
                          <div>Unripe: {entry.unripe ?? '-'}</div>
                          <div>Under Ripe: {entry.underRipe ?? '-'}</div>
                          <div>Waste: {entry.waste ?? '-'}</div>
                          <div>Long Stalk: {entry.longStalk ?? '-'}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {entry.weight ? `${entry.weight} kg` : '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {entry.cluster || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={clusterInputs[entry.id] || ''}
                            onChange={(e) => handleClusterInputChange(entry.id, e.target.value)}
                            placeholder="Cluster number"
                            className="w-32 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                          />
                          <button
                            onClick={() => handleClusterUpdate(entry.id)}
                            className="px-3 py-1 text-sm bg-purple-600 text-white rounded hover:bg-purple-700 focus:outline-none focus:ring-1 focus:ring-purple-500"
                          >
                            Update
                          </button>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${statusColors[status]}`}>
                          {statusLabels[status]}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(entry.timestamp).toLocaleString()}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
            {sortedEntries.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No entries available for cluster assignment.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAScreen;
