import React from "react";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Layout from "./components/Layout";
import Dashboard from "./components/Dashboard";
import OperatorScreen from "./components/OperatorScreen";
import GraderScreen from "./components/GraderScreen";
import FAScreen from "./components/FAScreen";

function App() {
  return (
    <Router>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/operator" element={<OperatorScreen />} />
          <Route path="/grader" element={<GraderScreen />} />
          <Route path="/fa" element={<FAScreen />} />
        </Routes>
      </Layout>
    </Router>
  );
}

export default App;
