export interface FruitEntry {
  id: string;
  farmerCode: string;
  unripe: number | null;
  underRipe: number | null;
  waste: number | null;
  longStalk: number | null;
  weight: number | null;
  cluster: string | null;
  timestamp: number;
}

export type EntryStatus = 'incomplete' | 'grading-complete' | 'weight-added' | 'complete';

export interface StatusColors {
  incomplete: string;
  'grading-complete': string;
  'weight-added': string;
  complete: string;
}

export type UserRole = 'operator' | 'grader' | 'fa' | 'dashboard';

export interface FilterOptions {
  searchTerm: string;
  statusFilter: EntryStatus | 'all';
}
