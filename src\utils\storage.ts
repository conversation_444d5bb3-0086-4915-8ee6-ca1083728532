import { FruitEntry } from '../types';

const STORAGE_KEY = 'fruit-collection-data';

export const loadEntries = (): FruitEntry[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];
    return JSON.parse(stored);
  } catch (error) {
    console.error('Error loading entries from localStorage:', error);
    return [];
  }
};

export const saveEntries = (entries: FruitEntry[]): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(entries));
  } catch (error) {
    console.error('Error saving entries to localStorage:', error);
  }
};

export const addEntry = (entry: FruitEntry): void => {
  const entries = loadEntries();
  entries.push(entry);
  saveEntries(entries);
};

export const updateEntry = (id: string, updates: Partial<FruitEntry>): void => {
  const entries = loadEntries();
  const index = entries.findIndex(entry => entry.id === id);
  if (index !== -1) {
    entries[index] = { ...entries[index], ...updates };
    saveEntries(entries);
  }
};

export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};
