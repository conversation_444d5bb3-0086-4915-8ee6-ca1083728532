import React, { useState, useEffect } from "react";
import { FruitEntry } from "../types";
import { loadEntries, updateEntry } from "../utils/storage";
import { getEntryStatus, statusColors, statusLabels } from "../utils/status";

interface GradingInputs {
  [entryId: string]: {
    unripe: string;
    underRipe: string;
    waste: string;
    longStalk: string;
  };
}

const GraderScreen: React.FC = () => {
  const [entries, setEntries] = useState<FruitEntry[]>([]);
  const [gradingInputs, setGradingInputs] = useState<GradingInputs>({});

  useEffect(() => {
    setEntries(loadEntries());
  }, []);

  const handleGradingInputChange = (
    entryId: string,
    field: keyof GradingInputs[string],
    value: string
  ) => {
    setGradingInputs((prev) => ({
      ...prev,
      [entryId]: {
        ...prev[entryId],
        [field]: value,
      },
    }));
  };

  const handleGradingUpdate = (entryId: string) => {
    const inputs = gradingInputs[entryId];
    if (!inputs) return;

    const updates: Partial<FruitEntry> = {};

    if (inputs.unripe !== undefined && inputs.unripe !== "") {
      updates.unripe = Number(inputs.unripe) || 0;
    }
    if (inputs.underRipe !== undefined && inputs.underRipe !== "") {
      updates.underRipe = Number(inputs.underRipe) || 0;
    }
    if (inputs.waste !== undefined && inputs.waste !== "") {
      updates.waste = Number(inputs.waste) || 0;
    }
    if (inputs.longStalk !== undefined && inputs.longStalk !== "") {
      updates.longStalk = Number(inputs.longStalk) || 0;
    }

    updateEntry(entryId, updates);
    setEntries(loadEntries());

    // Clear inputs for this entry
    setGradingInputs((prev) => ({
      ...prev,
      [entryId]: { unripe: "", underRipe: "", waste: "", longStalk: "" },
    }));
  };

  const getInputValue = (
    entryId: string,
    field: keyof GradingInputs[string]
  ) => {
    return gradingInputs[entryId]?.[field] || "";
  };

  // Sort entries by timestamp (newest first for display)
  const sortedEntries = [...entries].sort((a, b) => b.timestamp - a.timestamp);

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="bg-white shadow rounded-lg p-4 sm:p-6">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-6">
          Grader Panel
        </h2>

        <div>
          <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-3 sm:mb-4">
            Enter Grading Details
          </h3>
          {/* Desktop Table */}
          <div className="hidden lg:block overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Farmer Code
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Grading
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Unripe
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Under Ripe
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Waste
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Long Stalk
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {sortedEntries.map((entry) => {
                  const status = getEntryStatus(entry);
                  return (
                    <tr key={entry.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {entry.farmerCode}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="text-xs space-y-1">
                          <div>Unripe: {entry.unripe ?? "-"}</div>
                          <div>Under Ripe: {entry.underRipe ?? "-"}</div>
                          <div>Waste: {entry.waste ?? "-"}</div>
                          <div>Long Stalk: {entry.longStalk ?? "-"}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          step="0.1"
                          value={getInputValue(entry.id, "unripe")}
                          onChange={(e) =>
                            handleGradingInputChange(
                              entry.id,
                              "unripe",
                              e.target.value
                            )
                          }
                          placeholder="0.0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          step="0.1"
                          value={getInputValue(entry.id, "underRipe")}
                          onChange={(e) =>
                            handleGradingInputChange(
                              entry.id,
                              "underRipe",
                              e.target.value
                            )
                          }
                          placeholder="0.0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          step="0.1"
                          value={getInputValue(entry.id, "waste")}
                          onChange={(e) =>
                            handleGradingInputChange(
                              entry.id,
                              "waste",
                              e.target.value
                            )
                          }
                          placeholder="0.0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="number"
                          step="0.1"
                          value={getInputValue(entry.id, "longStalk")}
                          onChange={(e) =>
                            handleGradingInputChange(
                              entry.id,
                              "longStalk",
                              e.target.value
                            )
                          }
                          placeholder="0.0"
                          className="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() => handleGradingUpdate(entry.id)}
                          className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                          Update
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${statusColors[status]}`}
                        >
                          {statusLabels[status]}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
            {sortedEntries.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No entries available for grading.
              </div>
            )}
          </div>

          {/* Mobile Card Layout */}
          <div className="lg:hidden space-y-4">
            {sortedEntries.map((entry) => {
              const status = getEntryStatus(entry);
              return (
                <div
                  key={entry.id}
                  className="bg-gray-50 rounded-lg p-4 border"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="font-semibold text-lg">
                        {entry.farmerCode}
                      </div>
                    </div>
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${statusColors[status]}`}
                    >
                      {statusLabels[status]}
                    </span>
                  </div>

                  <div className="mb-4">
                    <div className="text-sm font-medium text-gray-700 mb-2">
                      Current Grading:
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>Unripe: {entry.unripe ?? "-"}</div>
                      <div>Under Ripe: {entry.underRipe ?? "-"}</div>
                      <div>Waste: {entry.waste ?? "-"}</div>
                      <div>Long Stalk: {entry.longStalk ?? "-"}</div>
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="text-sm font-medium text-gray-700">
                      Update Grading:
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">
                          Unripe
                        </label>
                        <input
                          type="number"
                          step="0.1"
                          value={getInputValue(entry.id, "unripe")}
                          onChange={(e) =>
                            handleGradingInputChange(
                              entry.id,
                              "unripe",
                              e.target.value
                            )
                          }
                          placeholder="0.0"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">
                          Under Ripe
                        </label>
                        <input
                          type="number"
                          step="0.1"
                          value={getInputValue(entry.id, "underRipe")}
                          onChange={(e) =>
                            handleGradingInputChange(
                              entry.id,
                              "underRipe",
                              e.target.value
                            )
                          }
                          placeholder="0.0"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">
                          Waste
                        </label>
                        <input
                          type="number"
                          step="0.1"
                          value={getInputValue(entry.id, "waste")}
                          onChange={(e) =>
                            handleGradingInputChange(
                              entry.id,
                              "waste",
                              e.target.value
                            )
                          }
                          placeholder="0.0"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-xs text-gray-500 mb-1">
                          Long Stalk
                        </label>
                        <input
                          type="number"
                          step="0.1"
                          value={getInputValue(entry.id, "longStalk")}
                          onChange={(e) =>
                            handleGradingInputChange(
                              entry.id,
                              "longStalk",
                              e.target.value
                            )
                          }
                          placeholder="0.0"
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    <button
                      onClick={() => handleGradingUpdate(entry.id)}
                      className="w-full px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      Update Grading
                    </button>
                  </div>
                </div>
              );
            })}
            {sortedEntries.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No entries available for grading.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GraderScreen;
