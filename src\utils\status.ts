import { FruitEntry, EntryStatus, StatusColors } from '../types';

export const getEntryStatus = (entry: FruitEntry): EntryStatus => {
  const hasGrading = entry.unripe !== null || entry.underRipe !== null || 
                     entry.waste !== null || entry.longStalk !== null;
  const hasWeight = entry.weight !== null;
  const hasCluster = entry.cluster !== null;

  if (hasCluster) return 'complete';
  if (hasWeight && hasGrading) return 'weight-added';
  if (hasGrading) return 'grading-complete';
  return 'incomplete';
};

export const statusColors: StatusColors = {
  incomplete: 'bg-red-100 text-red-800 border-red-200',
  'grading-complete': 'bg-orange-100 text-orange-800 border-orange-200',
  'weight-added': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  complete: 'bg-green-100 text-green-800 border-green-200'
};

export const statusLabels = {
  incomplete: 'Incomplete',
  'grading-complete': 'Grading Complete',
  'weight-added': 'Weight Added',
  complete: 'Complete'
};
